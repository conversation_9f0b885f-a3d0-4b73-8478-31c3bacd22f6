# Verazial Admin Deployment Package

## Files included:
- docker-compose.yml: Docker Compose configuration
- deployment-config.json: Application configuration (modify as needed)
- *.tar: Docker images
- deploy.ps1: Windows deployment script
- deploy.sh: Linux deployment script
- install-dependencies.sh: Linux dependency installation script

## Deployment Instructions:

### For Linux Servers:

1. **Install dependencies (if needed):**
   `ash
   chmod +x install-dependencies.sh
   ./install-dependencies.sh
   `

2. **Configure your environment:**
   Edit 'deployment-config.json' to match your target environment

3. **Deploy:**
   `ash
   chmod +x deploy.sh
   ./deploy.sh
   `

### For Windows Servers:

1. **Configure your environment:**
   Edit 'deployment-config.json' to match your target environment

2. **Deploy:**
   `powershell
   .\deploy.ps1
   `

### Manual Deployment:

1. **Configure your environment:**
   Edit 'deployment-config.json' to match your target environment:
   - grpcApiGateway: Your gRPC API Gateway URL
   - konektorAPI: Your Konektor API URL
   - credentialsAPI: Your Credentials API URL
   - credential_user: API username
   - credential_password: API password
   - port: External port for the application

2. **Load Docker images:**
   `
   docker load -i admin.tar
   # Load any other .tar files if present
   `

3. **Set environment variables (optional):**
   `
   # Windows PowerShell
   $env:PORT="20002"

   # Linux/Mac
   export PORT=20002
   `

4. **Deploy the application:**
   `
   docker compose up -d
   `

5. **Verify deployment:**
   - Check container status: docker compose ps
   - View logs: docker compose logs -f
   - Access application at: http://localhost:[PORT]

## Configuration Details:

The application will automatically read the configuration from 'deployment-config.json'
at startup and apply the settings to the running application.

You can modify the configuration file and restart the container to apply changes:
`
docker compose restart
`

## Troubleshooting:

- If the container fails to start, check logs: docker compose logs
- Ensure all required ports are available
- Verify the configuration file is valid JSON
- Check that all API endpoints in the config are accessible

Generated on: 2025-10-07 15:43:54
