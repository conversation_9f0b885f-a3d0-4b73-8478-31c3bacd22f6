# Enhanced Docker build and packaging script with configuration support
# This script builds the Docker images and packages them with configuration files

param(
    [string]$ConfigFile = "deployment-config.json",
    [switch]$NoBuild = $false,
    [switch]$Help = $false
)

if ($Help) {
    Write-Host "Usage: .\build-and-package.ps1 [-ConfigFile <path>] [-NoBuild] [-Help]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -ConfigFile   Path to the deployment configuration file (default: deployment-config.json)"
    Write-Host "  -NoBuild      Skip the build process and only package existing images"
    Write-Host "  -Help         Show this help message"
    Write-Host ""
    Write-Host "This script will:"
    Write-Host "  1. Build Docker images (unless -NoBuild is specified)"
    Write-Host "  2. Save images as .tar files"
    Write-Host "  3. Create a deployment package with docker-compose.yml and config file"
    exit 0
}

# Check if configuration file exists
if (-not (Test-Path $ConfigFile)) {
    Write-Host "Configuration file '$ConfigFile' not found!" -ForegroundColor Red
    Write-Host "Please ensure the configuration file exists or specify a different path with -ConfigFile parameter."
    exit 1
}

Write-Host "=== Verazial Admin Docker Build and Package Script ===" -ForegroundColor Green
Write-Host "Configuration file: $ConfigFile" -ForegroundColor Yellow

if (-not $NoBuild) {
    # Step 1: Build the images in the current directory
    Write-Host "Building Docker images..." -ForegroundColor Yellow
    docker compose build --no-cache

    if ($LASTEXITCODE -ne 0) {
        Write-Host "Docker build failed!" -ForegroundColor Red
        exit 1
    }
}

# Step 2: Get image names and tags from the current directory
Write-Host "Getting image information from docker-compose.yml..." -ForegroundColor Yellow
$composeFile = "docker-compose.yml"
$imageIDs = docker compose config | Select-String -Pattern "image:" | ForEach-Object { $_.ToString() -replace "^[^:]*: ", "" }

Write-Host "Found images:" -ForegroundColor Cyan
$imageIDs | ForEach-Object { Write-Host "  - $_" -ForegroundColor Cyan }

Write-Host "Processing image names..." -ForegroundColor Yellow
$savedImages = @()

foreach ($imageID in $imageIDs) {
    Write-Host "Processing image: $imageID" -ForegroundColor Cyan
    $imageName = $imageID -replace ":[^:]*$", ""

    # Extract the actual image name (remove group, if exists)
    if ($imageName -match "/") {
        $processedName = $imageName.Split("/")[-1]  # Get only the part after the last "/"
    } else {
        $processedName = $imageName
    }

    # Check if the image is locally built (matches "verazial/*")
    if ($imageID -match "^verazial/.+") {
        Write-Host "Skipping pull for locally built image $imageID." -ForegroundColor Green
    } else {
        Write-Host "Pulling external image $imageID..." -ForegroundColor Yellow
        docker pull $imageID
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Warning: Failed to pull $imageID. Continuing with local version if available." -ForegroundColor Yellow
        }
    }

    # Save the image locally with a clean tar filename
    $tarFile = "$processedName.tar"
    Write-Host "Saving image $imageID as $tarFile..." -ForegroundColor Yellow
    docker save $imageID -o $tarFile

    if ($LASTEXITCODE -eq 0) {
        $savedImages += $tarFile
        Write-Host "Successfully saved $tarFile" -ForegroundColor Green
    } else {
        Write-Host "Failed to save $imageID" -ForegroundColor Red
    }
}

# Step 3: Create deployment package
Write-Host "Creating deployment package..." -ForegroundColor Yellow

$deploymentDir = "deployment-package"
if (Test-Path $deploymentDir) {
    Remove-Item $deploymentDir -Recurse -Force
}
New-Item -ItemType Directory -Path $deploymentDir | Out-Null

# Copy necessary files to deployment package
Copy-Item "docker-compose.yml" "$deploymentDir/"
Copy-Item $ConfigFile "$deploymentDir/deployment-config.json"

# Copy deployment scripts for both Windows and Linux
if (Test-Path "deploy.ps1") {
    Copy-Item "deploy.ps1" "$deploymentDir/"
    Write-Host "Added Windows deployment script (deploy.ps1)" -ForegroundColor Green
}

if (Test-Path "deploy.sh") {
    Copy-Item "deploy.sh" "$deploymentDir/"
    Write-Host "Added Linux deployment script (deploy.sh)" -ForegroundColor Green
}

if (Test-Path "install-dependencies.sh") {
    Copy-Item "install-dependencies.sh" "$deploymentDir/"
    Write-Host "Added Linux dependency installation script" -ForegroundColor Green
}

# Copy all saved images
foreach ($tarFile in $savedImages) {
    if (Test-Path $tarFile) {
        Copy-Item $tarFile "$deploymentDir/"
        Write-Host "Added $tarFile to deployment package" -ForegroundColor Green
    }
}

# Create deployment instructions
$instructions = @"
# Verazial Admin Deployment Package

## Files included:
- docker-compose.yml: Docker Compose configuration
- deployment-config.json: Application configuration (modify as needed)
- *.tar: Docker images
- deploy.ps1: Windows deployment script
- deploy.sh: Linux deployment script
- install-dependencies.sh: Linux dependency installation script

## Deployment Instructions:

### For Linux Servers:

1. **Install dependencies (if needed):**
   ```bash
   chmod +x install-dependencies.sh
   ./install-dependencies.sh
   ```

2. **Configure your environment:**
   Edit 'deployment-config.json' to match your target environment

3. **Deploy:**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

### For Windows Servers:

1. **Configure your environment:**
   Edit 'deployment-config.json' to match your target environment

2. **Deploy:**
   ```powershell
   .\deploy.ps1
   ```

### Manual Deployment:

1. **Configure your environment:**
   Edit 'deployment-config.json' to match your target environment:
   - grpcApiGateway: Your gRPC API Gateway URL
   - konektorAPI: Your Konektor API URL
   - credentialsAPI: Your Credentials API URL
   - credential_user: API username
   - credential_password: API password
   - port: External port for the application

2. **Load Docker images:**
   ```
   docker load -i admin.tar
   # Load any other .tar files if present
   ```

3. **Set environment variables (optional):**
   ```
   # Windows PowerShell
   `$env:PORT="20002"

   # Linux/Mac
   export PORT=20002
   ```

4. **Deploy the application:**
   ```
   docker compose up -d
   ```

5. **Verify deployment:**
   - Check container status: `docker compose ps`
   - View logs: `docker compose logs -f`
   - Access application at: http://localhost:[PORT]

## Configuration Details:

The application will automatically read the configuration from 'deployment-config.json'
at startup and apply the settings to the running application.

You can modify the configuration file and restart the container to apply changes:
```
docker compose restart
```

## Troubleshooting:

- If the container fails to start, check logs: `docker compose logs`
- Ensure all required ports are available
- Verify the configuration file is valid JSON
- Check that all API endpoints in the config are accessible

Generated on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@

$instructions | Out-File -FilePath "$deploymentDir/README.md" -Encoding UTF8

Write-Host ""
Write-Host "=== Build and Package Completed Successfully! ===" -ForegroundColor Green
Write-Host ""
Write-Host "Deployment package created in: $deploymentDir" -ForegroundColor Cyan
Write-Host ""
Write-Host "Package contents:" -ForegroundColor Yellow
Get-ChildItem $deploymentDir | ForEach-Object {
    $size = if ($_.PSIsContainer) { "DIR" } else { "{0:N2} MB" -f ($_.Length / 1MB) }
    Write-Host "  - $($_.Name) ($size)" -ForegroundColor Cyan
}
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Copy the '$deploymentDir' folder to your target server" -ForegroundColor White
Write-Host "2. Edit 'deployment-config.json' for your environment" -ForegroundColor White
Write-Host "3. Follow the instructions in README.md" -ForegroundColor White
Write-Host ""
