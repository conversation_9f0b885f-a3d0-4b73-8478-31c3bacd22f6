FROM node:20-alpine AS build
WORKDIR /app
COPY package.json package-lock.json ./
COPY ngx-verazial-ui-lib-1.0.1.tgz ./
COPY drawflow-0.0.50.tgz ./
RUN npm install ngx-verazial-ui-lib-1.0.1.tgz
RUN npm install drawflow-0.0.50.tgz
RUN npm install

COPY . .

RUN npm install -g @angular/cli

RUN npm run build --prod

# Serve Application using Nginx Server
FROM nginx:alpine

# Install jq for JSON processing
RUN apk add --no-cache jq

COPY --from=build /app/dist/verazial-app/browser/ /usr/share/nginx/html

# Copy the default Nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY security-headers.conf /etc/nginx/security-headers.conf

# Copy configuration script
COPY scripts/configure-environment.sh /usr/local/bin/configure-environment.sh
RUN chmod +x /usr/local/bin/configure-environment.sh

# Create directory for configuration
RUN mkdir -p /app/config

EXPOSE 80

# Create startup script that configures environment and starts nginx
RUN echo '#!/bin/sh' > /usr/local/bin/startup.sh && \
    echo '/usr/local/bin/configure-environment.sh' >> /usr/local/bin/startup.sh && \
    echo 'nginx -g "daemon off;"' >> /usr/local/bin/startup.sh && \
    chmod +x /usr/local/bin/startup.sh

CMD ["/usr/local/bin/startup.sh"]
