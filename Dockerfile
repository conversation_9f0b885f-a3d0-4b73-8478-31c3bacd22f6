FROM node:20-alpine AS build
WORKDIR /app
COPY package.json package-lock.json ./
COPY ngx-verazial-ui-lib-1.0.1.tgz ./
COPY drawflow-0.0.50.tgz ./
RUN npm install ngx-verazial-ui-lib-1.0.1.tgz
RUN npm install drawflow-0.0.50.tgz
RUN npm install

COPY . .

RUN npm install -g @angular/cli

RUN npm run build --prod

# Serve Application using Nginx Server
FROM nginx:alpine

# Install jq for JSON processing
RUN apk add --no-cache jq

COPY --from=build /app/dist/verazial-app/browser/ /usr/share/nginx/html

# Copy the default Nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY security-headers.conf /etc/nginx/security-headers.conf

# Create configuration script directly in the image
RUN echo '#!/bin/bash' > /usr/local/bin/configure-environment.sh && \
    echo '' >> /usr/local/bin/configure-environment.sh && \
    echo '# Script to replace environment variables in the built Angular application' >> /usr/local/bin/configure-environment.sh && \
    echo '# This script runs inside the Docker container at startup' >> /usr/local/bin/configure-environment.sh && \
    echo '' >> /usr/local/bin/configure-environment.sh && \
    echo 'CONFIG_FILE="/app/config/deployment-config.json"' >> /usr/local/bin/configure-environment.sh && \
    echo 'TARGET_DIR="/usr/share/nginx/html"' >> /usr/local/bin/configure-environment.sh && \
    echo '' >> /usr/local/bin/configure-environment.sh && \
    echo 'echo "Starting environment configuration..."' >> /usr/local/bin/configure-environment.sh && \
    echo '' >> /usr/local/bin/configure-environment.sh && \
    echo 'if [ ! -f "$CONFIG_FILE" ]; then' >> /usr/local/bin/configure-environment.sh && \
    echo '    echo "Configuration file not found at $CONFIG_FILE"' >> /usr/local/bin/configure-environment.sh && \
    echo '    echo "Using default environment settings"' >> /usr/local/bin/configure-environment.sh && \
    echo '    exit 0' >> /usr/local/bin/configure-environment.sh && \
    echo 'fi' >> /usr/local/bin/configure-environment.sh && \
    echo '' >> /usr/local/bin/configure-environment.sh && \
    echo 'echo "Reading configuration from $CONFIG_FILE"' >> /usr/local/bin/configure-environment.sh && \
    echo '' >> /usr/local/bin/configure-environment.sh && \
    echo '# Read configuration values' >> /usr/local/bin/configure-environment.sh && \
    echo 'GRPC_API_GATEWAY=$(jq -r '\''.grpcApiGateway'\'' "$CONFIG_FILE")' >> /usr/local/bin/configure-environment.sh && \
    echo 'KONEKTOR_API=$(jq -r '\''.konektorAPI'\'' "$CONFIG_FILE")' >> /usr/local/bin/configure-environment.sh && \
    echo 'CREDENTIALS_API=$(jq -r '\''.credentialsAPI'\'' "$CONFIG_FILE")' >> /usr/local/bin/configure-environment.sh && \
    echo 'CREDENTIAL_USER=$(jq -r '\''.credential_user'\'' "$CONFIG_FILE")' >> /usr/local/bin/configure-environment.sh && \
    echo 'CREDENTIAL_PASSWORD=$(jq -r '\''.credential_password'\'' "$CONFIG_FILE")' >> /usr/local/bin/configure-environment.sh && \
    echo '' >> /usr/local/bin/configure-environment.sh && \
    echo 'echo "Applying configuration:"' >> /usr/local/bin/configure-environment.sh && \
    echo 'echo "  gRPC API Gateway: $GRPC_API_GATEWAY"' >> /usr/local/bin/configure-environment.sh && \
    echo 'echo "  Konektor API: $KONEKTOR_API"' >> /usr/local/bin/configure-environment.sh && \
    echo 'echo "  Credentials API: $CREDENTIALS_API"' >> /usr/local/bin/configure-environment.sh && \
    echo 'echo "  Credential User: $CREDENTIAL_USER"' >> /usr/local/bin/configure-environment.sh && \
    echo '' >> /usr/local/bin/configure-environment.sh && \
    echo '# Find all JavaScript files in the target directory' >> /usr/local/bin/configure-environment.sh && \
    echo 'find "$TARGET_DIR" -name "*.js" -type f | while read -r file; do' >> /usr/local/bin/configure-environment.sh && \
    echo '    echo "Processing file: $file"' >> /usr/local/bin/configure-environment.sh && \
    echo '' >> /usr/local/bin/configure-environment.sh && \
    echo '    # Replace the environment variables in the JavaScript files' >> /usr/local/bin/configure-environment.sh && \
    echo '    # Note: These placeholders will be set in the environment.ts file' >> /usr/local/bin/configure-environment.sh && \
    echo '    sed -i "s|__GRPC_API_GATEWAY__|$GRPC_API_GATEWAY|g" "$file"' >> /usr/local/bin/configure-environment.sh && \
    echo '    sed -i "s|__KONEKTOR_API__|$KONEKTOR_API|g" "$file"' >> /usr/local/bin/configure-environment.sh && \
    echo '    sed -i "s|__CREDENTIALS_API__|$CREDENTIALS_API|g" "$file"' >> /usr/local/bin/configure-environment.sh && \
    echo '    sed -i "s|__CREDENTIAL_USER__|$CREDENTIAL_USER|g" "$file"' >> /usr/local/bin/configure-environment.sh && \
    echo '    sed -i "s|__CREDENTIAL_PASSWORD__|$CREDENTIAL_PASSWORD|g" "$file"' >> /usr/local/bin/configure-environment.sh && \
    echo 'done' >> /usr/local/bin/configure-environment.sh && \
    echo '' >> /usr/local/bin/configure-environment.sh && \
    echo 'echo "Environment configuration completed successfully!"' >> /usr/local/bin/configure-environment.sh

RUN chmod +x /usr/local/bin/configure-environment.sh

# Create directory for configuration
RUN mkdir -p /app/config

EXPOSE 80

# Create startup script that configures environment and starts nginx
RUN echo '#!/bin/sh' > /usr/local/bin/startup.sh && \
    echo '/usr/local/bin/configure-environment.sh' >> /usr/local/bin/startup.sh && \
    echo 'nginx -g "daemon off;"' >> /usr/local/bin/startup.sh && \
    chmod +x /usr/local/bin/startup.sh

CMD ["/usr/local/bin/startup.sh"]
