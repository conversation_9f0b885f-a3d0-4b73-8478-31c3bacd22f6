#!/bin/bash

# Emergency patch script to fix the missing configuration script in the running container
# Run this on your Linux server to fix the immediate issue

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_color $GREEN "=== Emergency Container Patch Script ==="

# Check if Docker Compose is available
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
elif docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    print_color $RED "Docker Compose not found!"
    exit 1
fi

# Get container name
CONTAINER_NAME=$($DOCKER_COMPOSE ps -q angular-app 2>/dev/null)

if [[ -z "$CONTAINER_NAME" ]]; then
    print_color $RED "Container not running. Starting it first..."
    $DOCKER_COMPOSE up -d
    sleep 5
    CONTAINER_NAME=$($DOCKER_COMPOSE ps -q angular-app 2>/dev/null)

    if [[ -z "$CONTAINER_NAME" ]]; then
        print_color $RED "Failed to start container!"
        exit 1
    fi
fi

print_color $CYAN "Container ID: $CONTAINER_NAME"

# 1. Create the missing configuration script inside the container
print_color $YELLOW "1. Creating configuration script inside container..."

# Create the script using a simpler approach
docker exec "$CONTAINER_NAME" sh -c '
cat > /usr/local/bin/configure-environment.sh << "SCRIPT_END"
#!/bin/bash

# Script to replace environment variables in the built Angular application
# This script runs inside the Docker container at startup

CONFIG_FILE="/app/config/deployment-config.json"
TARGET_DIR="/usr/share/nginx/html"

echo "Starting environment configuration..."

if [ ! -f "$CONFIG_FILE" ]; then
    echo "Configuration file not found at $CONFIG_FILE"
    echo "Using default environment settings"
    exit 0
fi

echo "Reading configuration from $CONFIG_FILE"

# Read configuration values
GRPC_API_GATEWAY=$(jq -r ".grpcApiGateway" "$CONFIG_FILE")
KONEKTOR_API=$(jq -r ".konektorAPI" "$CONFIG_FILE")
CREDENTIALS_API=$(jq -r ".credentialsAPI" "$CONFIG_FILE")
CREDENTIAL_USER=$(jq -r ".credential_user" "$CONFIG_FILE")
CREDENTIAL_PASSWORD=$(jq -r ".credential_password" "$CONFIG_FILE")

echo "Applying configuration:"
echo "  gRPC API Gateway: $GRPC_API_GATEWAY"
echo "  Konektor API: $KONEKTOR_API"
echo "  Credentials API: $CREDENTIALS_API"
echo "  Credential User: $CREDENTIAL_USER"

# Find all JavaScript files in the target directory
find "$TARGET_DIR" -name "*.js" -type f | while read -r file; do
    echo "Processing file: $file"

    # Replace the environment variables in the JavaScript files
    # Note: These placeholders will be set in the environment.ts file
    sed -i "s|__GRPC_API_GATEWAY__|$GRPC_API_GATEWAY|g" "$file"
    sed -i "s|__KONEKTOR_API__|$KONEKTOR_API|g" "$file"
    sed -i "s|__CREDENTIALS_API__|$CREDENTIALS_API|g" "$file"
    sed -i "s|__CREDENTIAL_USER__|$CREDENTIAL_USER|g" "$file"
    sed -i "s|__CREDENTIAL_PASSWORD__|$CREDENTIAL_PASSWORD|g" "$file"
done

echo "Environment configuration completed successfully!"
SCRIPT_END
'

if [[ $? -eq 0 ]]; then
    print_color $GREEN "✓ Configuration script created successfully"
else
    print_color $RED "✗ Failed to create configuration script"
    exit 1
fi

# 2. Make the script executable
print_color $YELLOW "2. Making script executable..."
docker exec "$CONTAINER_NAME" chmod +x /usr/local/bin/configure-environment.sh

if [[ $? -eq 0 ]]; then
    print_color $GREEN "✓ Script made executable"
else
    print_color $RED "✗ Failed to make script executable"
    exit 1
fi

# 3. Check if jq is available
print_color $YELLOW "3. Checking if jq is available..."
if docker exec "$CONTAINER_NAME" which jq >/dev/null 2>&1; then
    print_color $GREEN "✓ jq is available"
else
    print_color $YELLOW "Installing jq..."
    docker exec "$CONTAINER_NAME" apk add --no-cache jq
    if [[ $? -eq 0 ]]; then
        print_color $GREEN "✓ jq installed successfully"
    else
        print_color $RED "✗ Failed to install jq"
        exit 1
    fi
fi

# 4. Check if configuration file is mounted
print_color $YELLOW "4. Checking configuration file..."
if docker exec "$CONTAINER_NAME" test -f /app/config/deployment-config.json; then
    print_color $GREEN "✓ Configuration file is mounted"
    echo "Configuration contents:"
    docker exec "$CONTAINER_NAME" cat /app/config/deployment-config.json
else
    print_color $RED "✗ Configuration file not found!"
    print_color $YELLOW "Please ensure deployment-config.json is in the current directory and restart the container"
    exit 1
fi

# 5. Run the configuration script
print_color $YELLOW "5. Running configuration script..."
if docker exec "$CONTAINER_NAME" /usr/local/bin/configure-environment.sh; then
    print_color $GREEN "✓ Configuration script executed successfully"
else
    print_color $RED "✗ Configuration script failed"
    exit 1
fi

# 6. Verify configuration was applied
print_color $YELLOW "6. Verifying configuration was applied..."
if docker exec "$CONTAINER_NAME" find /usr/share/nginx/html -name "*.js" -exec grep -l "__GRPC_API_GATEWAY__" {} \; | head -1 | grep -q .; then
    print_color $RED "✗ Placeholders still found in JavaScript files"
    print_color $YELLOW "This might be normal if the placeholders are in comments or strings"

    # Check for actual configuration values
    CONFIG_VALUE=$(docker exec "$CONTAINER_NAME" jq -r '.grpcApiGateway' /app/config/deployment-config.json)
    if docker exec "$CONTAINER_NAME" find /usr/share/nginx/html -name "*.js" -exec grep -l "$CONFIG_VALUE" {} \; | head -1 | grep -q .; then
        print_color $GREEN "✓ Configuration values found in JavaScript files"
    else
        print_color $YELLOW "Configuration values not found. This might need investigation."
    fi
else
    print_color $GREEN "✓ No placeholders found - configuration applied successfully"
fi

# 7. Update the startup script to include our configuration
print_color $YELLOW "7. Updating startup script..."
docker exec "$CONTAINER_NAME" sh -c '
cat > /usr/local/bin/startup.sh << "STARTUP_END"
#!/bin/sh
/usr/local/bin/configure-environment.sh
nginx -g "daemon off;"
STARTUP_END
'

docker exec "$CONTAINER_NAME" chmod +x /usr/local/bin/startup.sh

if [[ $? -eq 0 ]]; then
    print_color $GREEN "✓ Startup script updated"
else
    print_color $RED "✗ Failed to update startup script"
fi

echo ""
print_color $GREEN "=== Patch Completed Successfully! ==="
print_color $CYAN "The container has been patched and configuration applied."

echo ""
print_color $YELLOW "Next steps:"
print_color $WHITE "1. Test the application in your browser"
print_color $WHITE "2. Check if API calls are going to the correct endpoints"
print_color $WHITE "3. For permanent fix, rebuild the Docker image with the updated Dockerfile"

echo ""
print_color $YELLOW "To rebuild for permanent fix:"
print_color $WHITE "1. Update your Dockerfile (already done in the codebase)"
print_color $WHITE "2. Run: $DOCKER_COMPOSE build --no-cache"
print_color $WHITE "3. Run: $DOCKER_COMPOSE up -d"

echo ""
print_color $CYAN "Application should now be working with your configuration!"

# Show final status
print_color $YELLOW "Current container status:"
$DOCKER_COMPOSE ps
